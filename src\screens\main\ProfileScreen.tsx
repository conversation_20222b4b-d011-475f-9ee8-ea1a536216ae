import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  Switch,
  Dimensions,
  StatusBar,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  withTiming,
  FadeIn,
  SlideInDown,
  SlideInRight,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { useAppDispatch, useAppSelector } from '../../store';
import { signOut, updateProfile } from '../../store/slices/authSlice';
import { GoGoColors } from '../../../constants/Colors';
import { hapticFeedback } from '../../utils/animations';

const { width, height } = Dimensions.get('window');

// Modern Profile Option Component
interface ModernProfileOptionProps {
  icon: string;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  showArrow?: boolean;
  rightComponent?: React.ReactNode;
  index: number;
  variant?: 'default' | 'premium' | 'danger';
}

function ModernProfileOption({
  icon,
  title,
  subtitle,
  onPress,
  showArrow = true,
  rightComponent,
  index,
  variant = 'default'
}: ModernProfileOptionProps) {
  const scale = useSharedValue(1);

  const handlePressIn = () => {
    scale.value = withSpring(0.98);
    hapticFeedback.light();
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const getVariantStyles = () => {
    switch (variant) {
      case 'premium':
        return {
          backgroundColor: GoGoColors.premium + '10',
          borderColor: GoGoColors.premium + '20',
          iconColor: GoGoColors.premium,
        };
      case 'danger':
        return {
          backgroundColor: GoGoColors.error + '10',
          borderColor: GoGoColors.error + '20',
          iconColor: GoGoColors.error,
        };
      default:
        return {
          backgroundColor: GoGoColors.backgroundCard,
          borderColor: GoGoColors.border,
          iconColor: GoGoColors.primary,
        };
    }
  };

  const variantStyles = getVariantStyles();

  return (
    <Animated.View
      style={[
        styles.modernOptionItem,
        {
          backgroundColor: variantStyles.backgroundColor,
          borderColor: variantStyles.borderColor,
        },
        animatedStyle
      ]}
      entering={SlideInRight.delay(index * 100)}
    >
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={!onPress}
        activeOpacity={1}
        style={styles.optionTouchable}
      >
        <View style={styles.optionLeft}>
          <View style={[styles.iconContainer, { backgroundColor: variantStyles.iconColor + '15' }]}>
            <Ionicons name={icon as any} size={20} color={variantStyles.iconColor} />
          </View>
          <View style={styles.optionText}>
            <Text style={styles.modernOptionTitle}>{title}</Text>
            {subtitle && <Text style={styles.modernOptionSubtitle}>{subtitle}</Text>}
          </View>
        </View>
        {rightComponent || (showArrow && (
          <Ionicons name="chevron-forward" size={18} color={GoGoColors.textMuted} />
        ))}
      </TouchableOpacity>
    </Animated.View>
  );
}

// Stats Card Component
interface StatsCardProps {
  icon: string;
  value: string;
  label: string;
  color: string;
  index: number;
}

function StatsCard({ icon, value, label, color, index }: StatsCardProps) {
  return (
    <Animated.View style={styles.statsCard} entering={FadeIn.delay(index * 150)}>
      <LinearGradient
        colors={[color + '15', color + '05']}
        style={styles.statsGradient}
      >
        <View style={[styles.statsIcon, { backgroundColor: color + '20' }]}>
          <Ionicons name={icon as any} size={20} color={color} />
        </View>
        <Text style={styles.statsValue}>{value}</Text>
        <Text style={styles.statsLabel}>{label}</Text>
      </LinearGradient>
    </Animated.View>
  );
}

export default function ProfileScreen() {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  // State management
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [autoplayEnabled, setAutoplayEnabled] = useState(true);
  const [downloadQuality, setDownloadQuality] = useState('HD');
  const [refreshing, setRefreshing] = useState(false);

  // Animation values
  const headerScale = useSharedValue(1);
  const scrollY = useSharedValue(0);

  useEffect(() => {
    headerScale.value = withSpring(1, { damping: 15 });
  }, []);

  const handleSignOut = () => {
    hapticFeedback.medium();
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out of your GoGo account?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => {
            hapticFeedback.success();
            dispatch(signOut());
          }
        },
      ]
    );
  };

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    setTimeout(() => setRefreshing(false), 1000);
  };

  const ProfileOption = ({
    icon,
    title,
    subtitle,
    onPress,
    showArrow = true,
    rightComponent
  }: {
    icon: string;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    showArrow?: boolean;
    rightComponent?: React.ReactNode;
  }) => (
    <TouchableOpacity
      style={styles.optionItem}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.optionLeft}>
        <Ionicons name={icon as any} size={24} color={GoGoColors.highlightGold} />
        <View style={styles.optionText}>
          <Text style={styles.optionTitle}>{title}</Text>
          {subtitle && <Text style={styles.optionSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {rightComponent || (showArrow && (
        <Ionicons name="chevron-forward" size={20} color={GoGoColors.textMuted} />
      ))}
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Profile Header */}
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <Image
            source={{
              uri: user?.avatar_url || 'https://via.placeholder.com/100x100/1a1a1a/ffffff?text=User'
            }}
            style={styles.avatar}
          />
          <TouchableOpacity style={styles.editAvatarButton}>
            <Ionicons name="camera" size={16} color="white" />
          </TouchableOpacity>
        </View>
        <Text style={styles.userName}>{user?.full_name || 'User'}</Text>
        <Text style={styles.userEmail}>{user?.email || '<EMAIL>'}</Text>
        <Text style={styles.userRole}>
          {user?.role === 'creator' ? 'Content Creator' : 'Viewer'}
        </Text>
      </View>

      {/* Account Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Account</Text>
        <ProfileOption
          icon="person-outline"
          title="Edit Profile"
          subtitle="Update your personal information"
          onPress={() => {
            // Navigate to edit profile screen
            Alert.alert('Coming Soon', 'Profile editing will be available soon!');
          }}
        />
        <ProfileOption
          icon="card-outline"
          title="Subscription"
          subtitle="Manage your subscription plan"
          onPress={() => {
            Alert.alert('Coming Soon', 'Subscription management will be available soon!');
          }}
        />
        <ProfileOption
          icon="wallet-outline"
          title="Payment Methods"
          subtitle="Manage payment options"
          onPress={() => {
            Alert.alert('Coming Soon', 'Payment management will be available soon!');
          }}
        />
      </View>

      {/* Preferences Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Preferences</Text>
        <ProfileOption
          icon="notifications-outline"
          title="Notifications"
          subtitle="Push notifications and alerts"
          showArrow={false}
          rightComponent={
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.highlightGold }}
              thumbColor="white"
            />
          }
        />
        <ProfileOption
          icon="play-outline"
          title="Autoplay"
          subtitle="Automatically play next video"
          showArrow={false}
          rightComponent={
            <Switch
              value={autoplayEnabled}
              onValueChange={setAutoplayEnabled}
              trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.highlightGold }}
              thumbColor="white"
            />
          }
        />
        <ProfileOption
          icon="download-outline"
          title="Download Quality"
          subtitle="HD (1080p)"
          onPress={() => {
            Alert.alert('Coming Soon', 'Download quality settings will be available soon!');
          }}
        />
      </View>

      {/* Support Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Support</Text>
        <ProfileOption
          icon="help-circle-outline"
          title="Help Center"
          subtitle="Get help and support"
          onPress={() => {
            Alert.alert('Coming Soon', 'Help center will be available soon!');
          }}
        />
        <ProfileOption
          icon="chatbubble-outline"
          title="Contact Us"
          subtitle="Send feedback or report issues"
          onPress={() => {
            Alert.alert('Coming Soon', 'Contact form will be available soon!');
          }}
        />
        <ProfileOption
          icon="document-text-outline"
          title="Terms & Privacy"
          subtitle="Legal information"
          onPress={() => {
            Alert.alert('Coming Soon', 'Legal documents will be available soon!');
          }}
        />
      </View>

      {/* Sign Out */}
      <View style={styles.section}>
        <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
          <Ionicons name="log-out-outline" size={24} color={GoGoColors.error} />
          <Text style={styles.signOutText}>Sign Out</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.footer}>
        <Text style={styles.versionText}>GoGo v1.0.0</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: GoGoColors.backgroundLight,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: GoGoColors.highlightGold,
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: GoGoColors.backgroundDark,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: GoGoColors.textLight,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    color: GoGoColors.textMuted,
    marginBottom: 4,
  },
  userRole: {
    fontSize: 14,
    color: GoGoColors.highlightGold,
    fontWeight: '600',
  },
  section: {
    marginTop: 24,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textLight,
    marginBottom: 16,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionText: {
    marginLeft: 16,
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.textLight,
    marginBottom: 2,
  },
  optionSubtitle: {
    fontSize: 14,
    color: GoGoColors.textMuted,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  signOutText: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.error,
    marginLeft: 16,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  versionText: {
    fontSize: 14,
    color: GoGoColors.textMuted,
  },
});