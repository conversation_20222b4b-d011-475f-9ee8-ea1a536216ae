import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
  Dimensions,
  RefreshControl,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  withTiming,
  FadeIn,
  SlideInRight,
  SlideInDown,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { useAppSelector } from '../../store';
import { Video } from '../../types';
import { GoGoColors } from '../../../constants/Colors';
import { formatViewCount, formatDuration } from '../../utils/formatters';
import { hapticFeedback } from '../../utils/animations';

const { width } = Dimensions.get('window');

// Modern Library Video Card Component
interface ModernLibraryVideoProps {
  video: Video;
  onPress: (video: Video) => void;
  onRemove: (video: Video) => void;
  index: number;
  viewMode: 'list' | 'grid';
}

function ModernLibraryVideo({ video, onPress, onRemove, index, viewMode }: ModernLibraryVideoProps) {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(0);

  useEffect(() => {
    opacity.value = withTiming(1, { duration: 300 + index * 100 });
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.98);
    hapticFeedback.light();
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const handleMoreOptions = () => {
    hapticFeedback.light();
    Alert.alert(
      'Video Options',
      `What would you like to do with "${video.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Share', onPress: () => console.log('Share video') },
        { text: 'Download', onPress: () => console.log('Download video') },
        {
          text: 'Remove from Library',
          style: 'destructive',
          onPress: () => onRemove(video)
        },
      ]
    );
  };

  if (viewMode === 'grid') {
    return (
      <Animated.View style={[styles.gridVideoCard, animatedStyle]} entering={FadeIn.delay(index * 100)}>
        <TouchableOpacity
          onPress={() => onPress(video)}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={1}
          style={styles.gridCardTouchable}
        >
          <View style={styles.gridImageContainer}>
            <Image source={{ uri: video.thumbnail_url }} style={styles.gridImage} />
            {video.is_premium && (
              <View style={styles.gridPremiumBadge}>
                <Ionicons name="diamond" size={10} color="#FFFFFF" />
              </View>
            )}
            <View style={styles.gridPlayOverlay}>
              <Ionicons name="play" size={16} color="#FFFFFF" />
            </View>
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.7)']}
              style={styles.gridGradient}
            >
              <Text style={styles.gridDuration}>{formatDuration(video.duration)}</Text>
            </LinearGradient>
          </View>
          <View style={styles.gridContent}>
            <Text style={styles.gridTitle} numberOfLines={2}>{video.title}</Text>
            <Text style={styles.gridCreator} numberOfLines={1}>{video.creator.username}</Text>
            <Text style={styles.gridViews}>{formatViewCount(video.view_count)} views</Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity style={styles.gridMoreButton} onPress={handleMoreOptions}>
          <Ionicons name="ellipsis-horizontal" size={16} color={GoGoColors.textMuted} />
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={[styles.listVideoCard, animatedStyle]} entering={SlideInRight.delay(index * 50)}>
      <TouchableOpacity
        onPress={() => onPress(video)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
        style={styles.listCardTouchable}
      >
        <View style={styles.listImageContainer}>
          <Image source={{ uri: video.thumbnail_url }} style={styles.listImage} />
          {video.is_premium && (
            <View style={styles.listPremiumBadge}>
              <Ionicons name="diamond" size={8} color="#FFFFFF" />
            </View>
          )}
          <View style={styles.listPlayOverlay}>
            <Ionicons name="play" size={14} color="#FFFFFF" />
          </View>
          <View style={styles.listDurationBadge}>
            <Text style={styles.listDurationText}>{formatDuration(video.duration)}</Text>
          </View>
        </View>
        <View style={styles.listContent}>
          <Text style={styles.listTitle} numberOfLines={2}>{video.title}</Text>
          <Text style={styles.listCreator}>{video.creator.username}</Text>
          <View style={styles.listStats}>
            <Text style={styles.listStatText}>{formatViewCount(video.view_count)} views</Text>
            <Text style={styles.listStatDot}>•</Text>
            <Text style={styles.listStatText}>{video.category?.name || 'General'}</Text>
          </View>
        </View>
        <TouchableOpacity style={styles.listMoreButton} onPress={handleMoreOptions}>
          <Ionicons name="ellipsis-vertical" size={20} color={GoGoColors.textMuted} />
        </TouchableOpacity>
      </TouchableOpacity>
    </Animated.View>
  );
}

// Modern Tab Component
interface ModernTabProps {
  icon: string;
  label: string;
  count: number;
  isActive: boolean;
  onPress: () => void;
  index: number;
}

function ModernTab({ icon, label, count, isActive, onPress, index }: ModernTabProps) {
  const scale = useSharedValue(1);

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
    hapticFeedback.light();
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <Animated.View style={animatedStyle} entering={SlideInDown.delay(index * 100)}>
      <TouchableOpacity
        style={[styles.modernTab, isActive && styles.modernTabActive]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <LinearGradient
          colors={isActive ? [GoGoColors.primary, GoGoColors.primaryDark] : ['transparent', 'transparent']}
          style={styles.tabGradient}
        >
          <View style={styles.tabContent}>
            <Ionicons
              name={icon as any}
              size={20}
              color={isActive ? '#FFFFFF' : GoGoColors.textSecondary}
            />
            <Text style={[styles.modernTabText, isActive && styles.modernTabTextActive]}>
              {label}
            </Text>
            {count > 0 && (
              <View style={[styles.tabBadge, isActive && styles.tabBadgeActive]}>
                <Text style={[styles.tabBadgeText, isActive && styles.tabBadgeTextActive]}>
                  {count > 99 ? '99+' : count}
                </Text>
              </View>
            )}
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
}

interface Props {
  onVideoPress: (video: Video) => void;
}

export default function LibraryScreen({ onVideoPress }: Props) {
  const { user } = useAppSelector((state) => state.auth);

  // State management
  const [watchHistory, setWatchHistory] = useState<Video[]>([]);
  const [favorites, setFavorites] = useState<Video[]>([]);
  const [downloads, setDownloads] = useState<Video[]>([]);
  const [watchLater, setWatchLater] = useState<Video[]>([]);
  const [activeTab, setActiveTab] = useState<'history' | 'favorites' | 'downloads' | 'watchLater'>('history');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Animation values
  const headerOpacity = useSharedValue(1);
  const scrollY = useSharedValue(0);

  useEffect(() => {
    loadLibraryData();
  }, [user]);

  const loadLibraryData = async () => {
    // This would fetch from your backend
    // For now, using mock data
    setWatchHistory([]);
    setFavorites([]);
    setDownloads([]);
    setWatchLater([]);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadLibraryData();
    setRefreshing(false);
  };

  const handleRemoveFromLibrary = (video: Video) => {
    hapticFeedback.medium();
    Alert.alert(
      'Remove from Library',
      `Remove "${video.title}" from your ${getTabDisplayName(activeTab)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            hapticFeedback.success();
            switch (activeTab) {
              case 'history':
                setWatchHistory(prev => prev.filter(v => v.id !== video.id));
                break;
              case 'favorites':
                setFavorites(prev => prev.filter(v => v.id !== video.id));
                break;
              case 'downloads':
                setDownloads(prev => prev.filter(v => v.id !== video.id));
                break;
              case 'watchLater':
                setWatchLater(prev => prev.filter(v => v.id !== video.id));
                break;
            }
          },
        },
      ]
    );
  };

  const getCurrentData = () => {
    let data = [];
    switch (activeTab) {
      case 'history':
        data = watchHistory;
        break;
      case 'favorites':
        data = favorites;
        break;
      case 'downloads':
        data = downloads;
        break;
      case 'watchLater':
        data = watchLater;
        break;
      default:
        data = [];
    }

    // Filter by search query if provided
    if (searchQuery.trim()) {
      return data.filter(video =>
        video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        video.creator.username.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return data;
  };

  const getTabDisplayName = (tab: string) => {
    switch (tab) {
      case 'history': return 'Watch History';
      case 'favorites': return 'Favorites';
      case 'downloads': return 'Downloads';
      case 'watchLater': return 'Watch Later';
      default: return 'Library';
    }
  };

  const getEmptyMessage = () => {
    if (searchQuery.trim()) {
      return {
        icon: 'search-outline',
        title: 'No Results Found',
        message: `No videos found matching "${searchQuery}"`,
        action: 'Clear Search',
        onAction: () => setSearchQuery(''),
      };
    }

    switch (activeTab) {
      case 'history':
        return {
          icon: 'time-outline',
          title: 'No Watch History',
          message: 'Videos you watch will appear here automatically',
          action: 'Explore Content',
          onAction: () => console.log('Navigate to home'),
        };
      case 'favorites':
        return {
          icon: 'heart-outline',
          title: 'No Favorites Yet',
          message: 'Tap the heart icon on videos you love to save them here',
          action: 'Discover Videos',
          onAction: () => console.log('Navigate to search'),
        };
      case 'downloads':
        return {
          icon: 'download-outline',
          title: 'No Downloads',
          message: 'Download videos to watch offline anytime, anywhere',
          action: 'Browse Content',
          onAction: () => console.log('Navigate to home'),
        };
      case 'watchLater':
        return {
          icon: 'bookmark-outline',
          title: 'Watch Later is Empty',
          message: 'Save videos to watch later when you have more time',
          action: 'Find Videos',
          onAction: () => console.log('Navigate to search'),
        };
      default:
        return {
          icon: 'library-outline',
          title: 'Your Library is Empty',
          message: 'Start watching, liking, and downloading content to build your library',
          action: 'Get Started',
          onAction: () => console.log('Navigate to home'),
        };
    }
  };

  const renderVideo = ({ item, index }: { item: Video; index: number }) => (
    <ModernLibraryVideo
      video={item}
      onPress={onVideoPress}
      onRemove={handleRemoveFromLibrary}
      index={index}
      viewMode={viewMode}
    />
  );

  const toggleViewMode = () => {
    hapticFeedback.light();
    setViewMode(prev => prev === 'list' ? 'grid' : 'list');
  };

  const currentData = getCurrentData();
  const emptyMessage = getEmptyMessage();

  const headerAnimatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(
      scrollY.value,
      [0, 100],
      [0, -20],
      Extrapolate.CLAMP
    );
    return {
      transform: [{ translateY }],
      opacity: headerOpacity.value,
    };
  });

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />

      {/* Modern Header */}
      <Animated.View style={[styles.modernHeader, headerAnimatedStyle]} entering={FadeIn}>
        <LinearGradient
          colors={GoGoColors.backgroundGradient}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerTop}>
              <View style={styles.headerTitleContainer}>
                <Text style={styles.headerTitle}>My Library</Text>
                <Text style={styles.headerSubtitle}>
                  {user?.full_name ? `${user.full_name}'s collection` : 'Your personal collection'}
                </Text>
              </View>
              <View style={styles.headerActions}>
                <TouchableOpacity style={styles.headerActionButton} onPress={toggleViewMode}>
                  <Ionicons
                    name={viewMode === 'list' ? 'grid-outline' : 'list-outline'}
                    size={20}
                    color={GoGoColors.textPrimary}
                  />
                </TouchableOpacity>
                <TouchableOpacity style={styles.headerActionButton}>
                  <Ionicons name="search-outline" size={20} color={GoGoColors.textPrimary} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </LinearGradient>
      </Animated.View>

      {/* Modern Tab Navigation */}
      <View style={styles.modernTabContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabScrollContent}
        >
          <ModernTab
            icon="time-outline"
            label="History"
            count={watchHistory.length}
            isActive={activeTab === 'history'}
            onPress={() => setActiveTab('history')}
            index={0}
          />
          <ModernTab
            icon="heart-outline"
            label="Favorites"
            count={favorites.length}
            isActive={activeTab === 'favorites'}
            onPress={() => setActiveTab('favorites')}
            index={1}
          />
          <ModernTab
            icon="download-outline"
            label="Downloads"
            count={downloads.length}
            isActive={activeTab === 'downloads'}
            onPress={() => setActiveTab('downloads')}
            index={2}
          />
          <ModernTab
            icon="bookmark-outline"
            label="Watch Later"
            count={watchLater.length}
            isActive={activeTab === 'watchLater'}
            onPress={() => setActiveTab('watchLater')}
            index={3}
          />
        </ScrollView>
      </View>

      {/* Content Section */}
      <View style={styles.contentSection}>
        {currentData.length > 0 ? (
          <FlatList
            data={currentData}
            keyExtractor={(item) => item.id}
            renderItem={renderVideo}
            numColumns={viewMode === 'grid' ? 2 : 1}
            key={viewMode} // Force re-render when view mode changes
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.modernVideoList}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[GoGoColors.primary]}
                tintColor={GoGoColors.primary}
              />
            }
            onScroll={(event) => {
              scrollY.value = event.nativeEvent.contentOffset.y;
            }}
            scrollEventThrottle={16}
          />
        ) : (
          <Animated.View style={styles.modernEmptyContainer} entering={FadeIn.delay(300)}>
            <LinearGradient
              colors={[GoGoColors.primary + '10', GoGoColors.primary + '05']}
              style={styles.emptyGradient}
            >
              <Ionicons name={emptyMessage.icon as any} size={80} color={GoGoColors.primary} />
              <Text style={styles.modernEmptyTitle}>{emptyMessage.title}</Text>
              <Text style={styles.modernEmptyText}>{emptyMessage.message}</Text>
              {emptyMessage.action && (
                <TouchableOpacity
                  style={styles.emptyActionButton}
                  onPress={emptyMessage.onAction}
                >
                  <LinearGradient
                    colors={[GoGoColors.primary, GoGoColors.primaryDark]}
                    style={styles.emptyActionGradient}
                  >
                    <Text style={styles.emptyActionText}>{emptyMessage.action}</Text>
                  </LinearGradient>
                </TouchableOpacity>
              )}
            </LinearGradient>
          </Animated.View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  // Modern Header Styles
  modernHeader: {
    zIndex: 10,
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  headerContent: {
    paddingHorizontal: 20,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitleContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  headerActionButton: {
    width: 44,
    height: 44,
    borderRadius: 12,
    backgroundColor: GoGoColors.backgroundCard,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: GoGoColors.border,
  },
  // Modern Tab Styles
  modernTabContainer: {
    backgroundColor: GoGoColors.backgroundDark,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
    paddingVertical: 16,
  },
  tabScrollContent: {
    paddingHorizontal: 20,
    gap: 12,
  },
  modernTab: {
    borderRadius: 20,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: GoGoColors.border,
    minWidth: 120,
  },
  modernTabActive: {
    borderColor: GoGoColors.primary,
  },
  tabGradient: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  modernTabText: {
    fontSize: 14,
    fontWeight: '600',
    color: GoGoColors.textSecondary,
  },
  modernTabTextActive: {
    color: '#FFFFFF',
  },
  tabBadge: {
    backgroundColor: GoGoColors.backgroundLight,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 20,
    alignItems: 'center',
  },
  tabBadgeActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  tabBadgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: GoGoColors.textSecondary,
  },
  tabBadgeTextActive: {
    color: '#FFFFFF',
  },
  // Content Section
  contentSection: {
    flex: 1,
  },
  modernVideoList: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 16,
  },
  // Grid Video Card Styles
  gridVideoCard: {
    flex: 1,
    marginHorizontal: 4,
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: GoGoColors.border,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  gridCardTouchable: {
    flex: 1,
  },
  gridImageContainer: {
    position: 'relative',
    aspectRatio: 16 / 9,
  },
  gridImage: {
    width: '100%',
    height: '100%',
  },
  gridPremiumBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: GoGoColors.premium,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  gridPlayOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -16 }, { translateY: -16 }],
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  gridGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '30%',
    justifyContent: 'flex-end',
    paddingHorizontal: 8,
    paddingBottom: 8,
  },
  gridDuration: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
    alignSelf: 'flex-end',
  },
  gridContent: {
    padding: 12,
  },
  gridTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
    lineHeight: 18,
  },
  gridCreator: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
    marginBottom: 4,
  },
  gridViews: {
    fontSize: 11,
    color: GoGoColors.textMuted,
  },
  gridMoreButton: {
    position: 'absolute',
    top: 8,
    left: 8,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // List Video Card Styles
  listVideoCard: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: GoGoColors.border,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  listCardTouchable: {
    flexDirection: 'row',
    padding: 12,
  },
  listImageContainer: {
    position: 'relative',
    width: 120,
    height: 68,
    borderRadius: 8,
    overflow: 'hidden',
  },
  listImage: {
    width: '100%',
    height: '100%',
  },
  listPremiumBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: GoGoColors.premium,
    width: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  listPlayOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -14 }, { translateY: -14 }],
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  listDurationBadge: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  listDurationText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  listContent: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'space-between',
  },
  listTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    lineHeight: 20,
    marginBottom: 4,
  },
  listCreator: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    marginBottom: 8,
  },
  listStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listStatText: {
    fontSize: 12,
    color: GoGoColors.textMuted,
  },
  listStatDot: {
    fontSize: 12,
    color: GoGoColors.textMuted,
    marginHorizontal: 6,
  },
  listMoreButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  // Empty State Styles
  modernEmptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyGradient: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
    borderRadius: 20,
    width: '100%',
  },
  modernEmptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  modernEmptyText: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  emptyActionButton: {
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: GoGoColors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  emptyActionGradient: {
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  emptyActionText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});
