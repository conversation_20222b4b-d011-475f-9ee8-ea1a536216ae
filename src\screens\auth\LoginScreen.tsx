import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  Dimensions,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  withTiming,
  FadeIn,
  SlideInDown,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { useAppDispatch, useAppSelector } from '../../store';
import { signIn, clearError } from '../../store/slices/authSlice';
import { GoGoColors } from '../../../constants/Colors';
import { LoginForm } from '../../types';
import { hapticFeedback } from '../../utils/animations';

const { width, height } = Dimensions.get('window');

interface Props {
  onSwitchToRegister: () => void;
}

export default function LoginScreen({ onSwitchToRegister }: Props) {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);

  const [formData, setFormData] = useState<LoginForm>({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);

  // Animation values
  const logoScale = useSharedValue(0.8);
  const formOpacity = useSharedValue(0);
  const buttonScale = useSharedValue(1);
  const scrollY = useSharedValue(0);

  useEffect(() => {
    logoScale.value = withSpring(1, { damping: 15 });
    formOpacity.value = withTiming(1, { duration: 800 });
  }, []);

  const handleLogin = async () => {
    if (!formData.email || !formData.password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    hapticFeedback.medium();
    buttonScale.value = withSpring(0.95, {}, () => {
      buttonScale.value = withSpring(1);
    });

    try {
      await dispatch(signIn(formData)).unwrap();
    } catch (error: any) {
      hapticFeedback.error();
      Alert.alert('Login Failed', error);
    }
  };

  const updateFormData = (field: keyof LoginForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) {
      dispatch(clearError());
    }
  };

  const handleDemoLogin = () => {
    hapticFeedback.light();
    setFormData({ email: '<EMAIL>', password: 'demo123' });
    setTimeout(() => handleLogin(), 100);
  };

  // Animated styles
  const logoAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: logoScale.value }],
  }));

  const formAnimatedStyle = useAnimatedStyle(() => ({
    opacity: formOpacity.value,
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScale.value }],
  }));

  const headerAnimatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(
      scrollY.value,
      [0, 100],
      [0, -50],
      Extrapolate.CLAMP
    );
    return {
      transform: [{ translateY }],
    };
  });

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* Background Gradient */}
      <LinearGradient
        colors={['#FFFFFF', '#F8F9FA', '#E3F2FD']}
        style={styles.backgroundGradient}
      />

      {/* Floating Elements */}
      <View style={styles.floatingElements}>
        <Animated.View style={[styles.floatingCircle, styles.circle1]} entering={FadeIn.delay(500)} />
        <Animated.View style={[styles.floatingCircle, styles.circle2]} entering={FadeIn.delay(700)} />
        <Animated.View style={[styles.floatingCircle, styles.circle3]} entering={FadeIn.delay(900)} />
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          onScroll={(event) => {
            scrollY.value = event.nativeEvent.contentOffset.y;
          }}
          scrollEventThrottle={16}
        >
          {/* Header Section */}
          <Animated.View style={[styles.headerSection, headerAnimatedStyle]}>
            <Animated.View style={[styles.logoContainer, logoAnimatedStyle]} entering={FadeIn.delay(200)}>
              <LinearGradient
                colors={[GoGoColors.primary, GoGoColors.primaryDark]}
                style={styles.logoGradient}
              >
                <Text style={styles.logo}>GoGo</Text>
              </LinearGradient>
              <Text style={styles.tagline}>Welcome back to your streaming world</Text>
              <View style={styles.welcomeIcons}>
                <Ionicons name="play-circle" size={24} color={GoGoColors.primary} />
                <Ionicons name="heart" size={20} color={GoGoColors.error} />
                <Ionicons name="star" size={22} color={GoGoColors.highlightGold} />
              </View>
            </Animated.View>
          </Animated.View>

          {/* Form Section */}
          <Animated.View style={[styles.formSection, formAnimatedStyle]} entering={SlideInDown.delay(400)}>
            <View style={styles.formCard}>
              <Text style={styles.formTitle}>Sign In</Text>
              <Text style={styles.formSubtitle}>Continue your entertainment journey</Text>

              {/* Email Input */}
              <Animated.View entering={SlideInDown.delay(600)}>
                <View style={[
                  styles.modernInputContainer,
                  focusedField === 'email' && styles.modernInputContainerFocused
                ]}>
                  <View style={styles.inputIconContainer}>
                    <Ionicons
                      name="mail-outline"
                      size={20}
                      color={focusedField === 'email' ? GoGoColors.primary : GoGoColors.textMuted}
                    />
                  </View>
                  <TextInput
                    style={styles.modernInput}
                    placeholder="Enter your email"
                    placeholderTextColor={GoGoColors.textMuted}
                    value={formData.email}
                    onChangeText={(text) => updateFormData('email', text)}
                    onFocus={() => {
                      setFocusedField('email');
                      hapticFeedback.light();
                    }}
                    onBlur={() => setFocusedField(null)}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                </View>
              </Animated.View>

              {/* Password Input */}
              <Animated.View entering={SlideInDown.delay(700)}>
                <View style={[
                  styles.modernInputContainer,
                  focusedField === 'password' && styles.modernInputContainerFocused
                ]}>
                  <View style={styles.inputIconContainer}>
                    <Ionicons
                      name="lock-closed-outline"
                      size={20}
                      color={focusedField === 'password' ? GoGoColors.primary : GoGoColors.textMuted}
                    />
                  </View>
                  <TextInput
                    style={styles.modernInput}
                    placeholder="Enter your password"
                    placeholderTextColor={GoGoColors.textMuted}
                    value={formData.password}
                    onChangeText={(text) => updateFormData('password', text)}
                    onFocus={() => {
                      setFocusedField('password');
                      hapticFeedback.light();
                    }}
                    onBlur={() => setFocusedField(null)}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                  />
                  <TouchableOpacity
                    style={styles.passwordToggle}
                    onPress={() => {
                      setShowPassword(!showPassword);
                      hapticFeedback.light();
                    }}
                  >
                    <Ionicons
                      name={showPassword ? "eye-off-outline" : "eye-outline"}
                      size={20}
                      color={GoGoColors.textMuted}
                    />
                  </TouchableOpacity>
                </View>
              </Animated.View>

              {/* Error Display */}
              {error && (
                <Animated.View style={styles.errorContainer} entering={SlideInDown}>
                  <Ionicons name="alert-circle" size={20} color={GoGoColors.error} />
                  <Text style={styles.errorText}>{error}</Text>
                </Animated.View>
              )}

              {/* Sign In Button */}
              <Animated.View style={buttonAnimatedStyle} entering={SlideInDown.delay(800)}>
                <TouchableOpacity
                  style={[styles.modernLoginButton, isLoading && styles.disabledButton]}
                  onPress={handleLogin}
                  disabled={isLoading}
                  activeOpacity={0.9}
                >
                  <LinearGradient
                    colors={[GoGoColors.primary, GoGoColors.primaryDark]}
                    style={styles.modernButtonGradient}
                  >
                    {isLoading ? (
                      <View style={styles.loadingContainer}>
                        <ActivityIndicator size="small" color="#FFFFFF" />
                        <Text style={styles.modernButtonText}>Signing In...</Text>
                      </View>
                    ) : (
                      <View style={styles.buttonContent}>
                        <Ionicons name="arrow-forward" size={20} color="#FFFFFF" />
                        <Text style={styles.modernButtonText}>Sign In</Text>
                      </View>
                    )}
                  </LinearGradient>
                </TouchableOpacity>
              </Animated.View>

              {/* Forgot Password */}
              <Animated.View entering={SlideInDown.delay(900)}>
                <TouchableOpacity style={styles.modernForgotPassword}>
                  <Text style={styles.modernForgotPasswordText}>Forgot your password?</Text>
                </TouchableOpacity>
              </Animated.View>

              {/* Divider */}
              <Animated.View style={styles.dividerContainer} entering={SlideInDown.delay(1000)}>
                <View style={styles.dividerLine} />
                <Text style={styles.dividerText}>or</Text>
                <View style={styles.dividerLine} />
              </Animated.View>

              {/* Demo Mode Button */}
              <Animated.View entering={SlideInDown.delay(1100)}>
                <TouchableOpacity
                  style={styles.modernDemoButton}
                  onPress={handleDemoLogin}
                  activeOpacity={0.8}
                >
                  <View style={styles.demoButtonContent}>
                    <Ionicons name="play-circle" size={20} color={GoGoColors.primary} />
                    <Text style={styles.modernDemoButtonText}>Try Demo Mode</Text>
                  </View>
                  <Text style={styles.demoButtonSubtext}>Experience GoGo without signing up</Text>
                </TouchableOpacity>
              </Animated.View>
            </View>
          </Animated.View>

          {/* Footer Section */}
          <Animated.View style={styles.footerSection} entering={SlideInDown.delay(1200)}>
            <View style={styles.switchContainer}>
              <Text style={styles.switchText}>Don't have an account? </Text>
              <TouchableOpacity
                onPress={() => {
                  hapticFeedback.light();
                  onSwitchToRegister();
                }}
              >
                <Text style={styles.switchLink}>Sign Up</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.footerInfo}>
              <Text style={styles.footerText}>
                By signing in, you agree to our Terms of Service and Privacy Policy
              </Text>
            </View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  floatingElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  floatingCircle: {
    position: 'absolute',
    borderRadius: 100,
    opacity: 0.1,
  },
  circle1: {
    width: 200,
    height: 200,
    backgroundColor: GoGoColors.primary,
    top: -100,
    right: -100,
  },
  circle2: {
    width: 150,
    height: 150,
    backgroundColor: GoGoColors.highlightGold,
    bottom: 100,
    left: -75,
  },
  circle3: {
    width: 100,
    height: 100,
    backgroundColor: GoGoColors.error,
    top: height * 0.3,
    right: 50,
  },
  keyboardView: {
    flex: 1,
    zIndex: 2,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  // Header Section
  headerSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  logoGradient: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 20,
    marginBottom: 16,
    elevation: 4,
    shadowColor: GoGoColors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  logo: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 2,
  },
  tagline: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  welcomeIcons: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'center',
  },
  // Form Section
  formSection: {
    flex: 1,
  },
  formCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: 32,
    marginHorizontal: 8,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  formTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    textAlign: 'center',
    marginBottom: 8,
  },
  formSubtitle: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  // Modern Input Styles
  modernInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 16,
    marginBottom: 20,
    borderWidth: 2,
    borderColor: 'transparent',
    height: 56,
    paddingHorizontal: 4,
  },
  modernInputContainerFocused: {
    borderColor: GoGoColors.primary,
    backgroundColor: '#FFFFFF',
    elevation: 2,
    shadowColor: GoGoColors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  inputIconContainer: {
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 4,
  },
  modernInput: {
    flex: 1,
    color: GoGoColors.textPrimary,
    fontSize: 16,
    fontWeight: '500',
    paddingVertical: 16,
    paddingRight: 16,
  },
  passwordToggle: {
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 4,
  },
  // Error Styles
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(244, 67, 54, 0.2)',
  },
  errorText: {
    color: GoGoColors.error,
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
    fontWeight: '500',
  },
  // Button Styles
  modernLoginButton: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 24,
    elevation: 4,
    shadowColor: GoGoColors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  disabledButton: {
    opacity: 0.6,
  },
  modernButtonGradient: {
    paddingVertical: 18,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  modernButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  // Forgot Password
  modernForgotPassword: {
    alignItems: 'center',
    marginBottom: 24,
  },
  modernForgotPasswordText: {
    color: GoGoColors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  // Divider
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
  // Demo Button
  modernDemoButton: {
    backgroundColor: 'rgba(25, 118, 210, 0.05)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(25, 118, 210, 0.1)',
    alignItems: 'center',
  },
  demoButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  modernDemoButtonText: {
    color: GoGoColors.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  demoButtonSubtext: {
    color: GoGoColors.textMuted,
    fontSize: 12,
    textAlign: 'center',
  },
  // Footer Section
  footerSection: {
    marginTop: 40,
    alignItems: 'center',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchText: {
    color: GoGoColors.textSecondary,
    fontSize: 16,
  },
  switchLink: {
    color: GoGoColors.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  footerInfo: {
    paddingHorizontal: 20,
  },
  footerText: {
    color: GoGoColors.textMuted,
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
});
