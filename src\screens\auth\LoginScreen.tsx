import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  Dimensions,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  withTiming,
  FadeIn,
  SlideInDown,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { useAppDispatch, useAppSelector } from '../../store';
import { signIn, clearError } from '../../store/slices/authSlice';
import { GoGoColors } from '../../../constants/Colors';
import { LoginForm } from '../../types';
import { hapticFeedback } from '../../utils/animations';

const { width, height } = Dimensions.get('window');

interface Props {
  onSwitchToRegister: () => void;
}

export default function LoginScreen({ onSwitchToRegister }: Props) {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);

  const [formData, setFormData] = useState<LoginForm>({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);

  // Animation values
  const logoScale = useSharedValue(0.8);
  const formOpacity = useSharedValue(0);
  const buttonScale = useSharedValue(1);
  const scrollY = useSharedValue(0);

  useEffect(() => {
    logoScale.value = withSpring(1, { damping: 15 });
    formOpacity.value = withTiming(1, { duration: 800 });
  }, []);

  const handleLogin = async () => {
    if (!formData.email || !formData.password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    hapticFeedback.medium();
    buttonScale.value = withSpring(0.95, {}, () => {
      buttonScale.value = withSpring(1);
    });

    try {
      await dispatch(signIn(formData)).unwrap();
    } catch (error: any) {
      hapticFeedback.error();
      Alert.alert('Login Failed', error);
    }
  };

  const updateFormData = (field: keyof LoginForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) {
      dispatch(clearError());
    }
  };

  const handleDemoLogin = () => {
    hapticFeedback.light();
    setFormData({ email: '<EMAIL>', password: 'demo123' });
    setTimeout(() => handleLogin(), 100);
  };

  // Animated styles
  const logoAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: logoScale.value }],
  }));

  const formAnimatedStyle = useAnimatedStyle(() => ({
    opacity: formOpacity.value,
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScale.value }],
  }));

  const headerAnimatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(
      scrollY.value,
      [0, 100],
      [0, -50],
      Extrapolate.CLAMP
    );
    return {
      transform: [{ translateY }],
    };
  });

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          onScroll={(event) => {
            scrollY.value = event.nativeEvent.contentOffset.y;
          }}
          scrollEventThrottle={16}
        >
          {/* Twitter-style Header */}
          <Animated.View style={[styles.twitterHeader]} entering={FadeIn.delay(200)}>
            <Animated.View style={[styles.twitterLogoContainer, logoAnimatedStyle]}>
              {/* GoGo Logo with Twitter-style design */}
              <View style={styles.twitterLogo}>
                <LinearGradient
                  colors={[GoGoColors.highlightGold, '#FFD700']}
                  style={styles.twitterLogoGradient}
                >
                  <Text style={styles.twitterLogoText}>GoGo</Text>
                </LinearGradient>
              </View>
            </Animated.View>
          </Animated.View>

          {/* Twitter-style Main Content */}
          <Animated.View style={[styles.twitterMainContent]} entering={SlideInDown.delay(400)}>
            {/* Twitter-style Welcome Text */}
            <View style={styles.twitterWelcomeSection}>
              <Text style={styles.twitterMainTitle}>Sign in to GoGo</Text>
            </View>

            {/* Twitter-style Form */}
            <View style={styles.twitterFormContainer}>
              {/* Email Input - Twitter Style */}
              <Animated.View entering={SlideInDown.delay(600)}>
                <View style={[
                  styles.twitterInputContainer,
                  focusedField === 'email' && styles.twitterInputContainerFocused
                ]}>
                  <TextInput
                    style={styles.twitterInput}
                    placeholder=""
                    placeholderTextColor="#536471"
                    value={formData.email}
                    onChangeText={(text) => updateFormData('email', text)}
                    onFocus={() => {
                      setFocusedField('email');
                      hapticFeedback.light();
                    }}
                    onBlur={() => setFocusedField(null)}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <Text style={[
                    styles.twitterInputLabel,
                    (focusedField === 'email' || formData.email) && styles.twitterInputLabelActive
                  ]}>
                    Email or username
                  </Text>
                </View>
              </Animated.View>

              {/* Password Input - Twitter Style */}
              <Animated.View entering={SlideInDown.delay(700)}>
                <View style={[
                  styles.twitterInputContainer,
                  focusedField === 'password' && styles.twitterInputContainerFocused
                ]}>
                  <TextInput
                    style={styles.twitterInput}
                    placeholder=""
                    placeholderTextColor="#536471"
                    value={formData.password}
                    onChangeText={(text) => updateFormData('password', text)}
                    onFocus={() => {
                      setFocusedField('password');
                      hapticFeedback.light();
                    }}
                    onBlur={() => setFocusedField(null)}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                  />
                  <Text style={[
                    styles.twitterInputLabel,
                    (focusedField === 'password' || formData.password) && styles.twitterInputLabelActive
                  ]}>
                    Password
                  </Text>
                  <TouchableOpacity
                    style={styles.twitterPasswordToggle}
                    onPress={() => {
                      setShowPassword(!showPassword);
                      hapticFeedback.light();
                    }}
                  >
                    <Ionicons
                      name={showPassword ? "eye-off-outline" : "eye-outline"}
                      size={20}
                      color="#536471"
                    />
                  </TouchableOpacity>
                </View>
              </Animated.View>

              {/* Error Display - Twitter Style */}
              {error && (
                <Animated.View style={styles.twitterErrorContainer} entering={SlideInDown}>
                  <Text style={styles.twitterErrorText}>{error}</Text>
                </Animated.View>
              )}

              {/* Twitter-style Sign In Button */}
              <Animated.View style={buttonAnimatedStyle} entering={SlideInDown.delay(800)}>
                <TouchableOpacity
                  style={[styles.twitterLoginButton, isLoading && styles.twitterDisabledButton]}
                  onPress={handleLogin}
                  disabled={isLoading}
                  activeOpacity={0.8}
                >
                  {isLoading ? (
                    <View style={styles.twitterLoadingContainer}>
                      <ActivityIndicator size="small" color="#FFFFFF" />
                      <Text style={styles.twitterButtonText}>Signing in...</Text>
                    </View>
                  ) : (
                    <Text style={styles.twitterButtonText}>Sign in</Text>
                  )}
                </TouchableOpacity>
              </Animated.View>

              {/* Twitter-style Forgot Password */}
              <Animated.View entering={SlideInDown.delay(900)}>
                <TouchableOpacity style={styles.twitterForgotPassword}>
                  <Text style={styles.twitterForgotPasswordText}>Forgot password?</Text>
                </TouchableOpacity>
              </Animated.View>
            </View>

            {/* Twitter-style Divider */}
            <Animated.View style={styles.twitterDividerContainer} entering={SlideInDown.delay(1000)}>
              <View style={styles.twitterDividerLine} />
              <Text style={styles.twitterDividerText}>or</Text>
              <View style={styles.twitterDividerLine} />
            </Animated.View>

            {/* Twitter-style Demo Button */}
            <Animated.View entering={SlideInDown.delay(1100)}>
              <TouchableOpacity
                style={styles.twitterDemoButton}
                onPress={handleDemoLogin}
                activeOpacity={0.8}
              >
                <Text style={styles.twitterDemoButtonText}>Try Demo Mode</Text>
              </TouchableOpacity>
            </Animated.View>
          </Animated.View>

          {/* Twitter-style Footer */}
          <Animated.View style={styles.twitterFooter} entering={SlideInDown.delay(1200)}>
            <View style={styles.twitterSwitchContainer}>
              <Text style={styles.twitterSwitchText}>Don't have an account? </Text>
              <TouchableOpacity
                onPress={() => {
                  hapticFeedback.light();
                  onSwitchToRegister();
                }}
              >
                <Text style={styles.twitterSwitchLink}>Sign up</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
    paddingVertical: 40,
  },
  // Twitter-style Header
  twitterHeader: {
    alignItems: 'center',
    marginBottom: 48,
  },
  twitterLogoContainer: {
    alignItems: 'center',
  },
  twitterLogo: {
    borderRadius: 50,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: GoGoColors.highlightGold,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  twitterLogoGradient: {
    width: 80,
    height: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  twitterLogoText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 1,
  },
  // Twitter-style Main Content
  twitterMainContent: {
    flex: 1,
  },
  twitterWelcomeSection: {
    marginBottom: 32,
  },
  twitterMainTitle: {
    fontSize: 31,
    fontWeight: 'bold',
    color: '#0F1419',
    lineHeight: 36,
    marginBottom: 8,
    textAlign: 'center',
  },
  // Twitter-style Form
  twitterFormContainer: {
    marginBottom: 20,
  },
  // Twitter-style Input
  twitterInputContainer: {
    position: 'relative',
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#CFD9DE',
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  twitterInputContainerFocused: {
    borderColor: GoGoColors.highlightGold,
    borderWidth: 2,
  },
  twitterInput: {
    fontSize: 17,
    color: '#0F1419',
    paddingHorizontal: 12,
    paddingTop: 24,
    paddingBottom: 8,
    minHeight: 56,
  },
  twitterInputLabel: {
    position: 'absolute',
    left: 12,
    top: 16,
    fontSize: 17,
    color: '#536471',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 4,
  },
  twitterInputLabelActive: {
    top: -8,
    fontSize: 13,
    color: GoGoColors.highlightGold,
  },
  twitterPasswordToggle: {
    position: 'absolute',
    right: 12,
    top: 18,
    padding: 4,
  },
  // Twitter-style Error
  twitterErrorContainer: {
    backgroundColor: '#FEF7F7',
    borderWidth: 1,
    borderColor: '#F4212E',
    borderRadius: 4,
    padding: 12,
    marginBottom: 20,
  },
  twitterErrorText: {
    color: '#F4212E',
    fontSize: 15,
    lineHeight: 20,
  },
  // Twitter-style Buttons
  twitterLoginButton: {
    backgroundColor: '#0F1419',
    borderRadius: 50,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    minHeight: 44,
  },
  twitterDisabledButton: {
    backgroundColor: '#8B98A5',
  },
  twitterLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  twitterButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: 'bold',
  },
  // Twitter-style Forgot Password
  twitterForgotPassword: {
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  twitterForgotPasswordText: {
    color: GoGoColors.highlightGold,
    fontSize: 15,
    lineHeight: 20,
  },
  // Twitter-style Divider
  twitterDividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  twitterDividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#CFD9DE',
  },
  twitterDividerText: {
    marginHorizontal: 16,
    fontSize: 15,
    color: '#536471',
  },
  // Twitter-style Demo Button
  twitterDemoButton: {
    borderWidth: 1,
    borderColor: '#CFD9DE',
    borderRadius: 50,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    minHeight: 44,
    backgroundColor: '#FFFFFF',
  },
  twitterDemoButtonText: {
    color: '#0F1419',
    fontSize: 17,
    fontWeight: 'bold',
  },
  // Twitter-style Footer
  twitterFooter: {
    marginTop: 40,
    alignItems: 'center',
  },
  twitterSwitchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  twitterSwitchText: {
    fontSize: 15,
    color: '#536471',
    lineHeight: 20,
  },
  twitterSwitchLink: {
    fontSize: 15,
    color: GoGoColors.highlightGold,
    fontWeight: '400',
    lineHeight: 20,
  },
});
